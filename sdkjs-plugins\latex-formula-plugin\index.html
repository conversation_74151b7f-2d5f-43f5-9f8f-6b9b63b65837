<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LaTeX Formula Plugin</title>
    <script src="https://onlyoffice.github.io/sdkjs-plugins/v1/plugins.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/3.2.2/es5/tex-mml-chtml.js"></script>
    <style>
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
        min-height: 100vh;
        color: #333;
      }

      .container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.18);
        max-width: 100%;
        animation: slideIn 0.5s ease-out;
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(-20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .header {
        text-align: center;
        margin-bottom: 25px;
      }

      .header h2 {
        color: #4a5568;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 8px;
      }

      .header p {
        color: #718096;
        font-size: 14px;
      }

      .form-group {
        margin-bottom: 20px;
      }

      label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #4a5568;
        font-size: 14px;
      }

      .textarea-container {
        position: relative;
      }

      textarea {
        width: 100%;
        min-height: 120px;
        padding: 12px;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        font-size: 14px;
        resize: vertical;
        transition: all 0.3s ease;
        background: #fff;
      }

      textarea:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        transform: translateY(-1px);
      }

      .preview-section {
        background: #f8fafc;
        border: 2px dashed #cbd5e0;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        min-height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
      }

      .preview-section:hover {
        background: #edf2f7;
        border-color: #a0aec0;
      }

      .preview-content {
        font-size: 18px;
        color: #2d3748;
      }

      .preview-placeholder {
        color: #a0aec0;
        font-style: italic;
      }

      .button-group {
        display: flex;
        gap: 12px;
        justify-content: center;
        margin-top: 25px;
      }

      .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: relative;
        overflow: hidden;
      }

      .btn:before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
      }

      .btn:hover:before {
        left: 100%;
      }

      .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
      }

      .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }

      .btn-secondary {
        background: #e2e8f0;
        color: #4a5568;
        border: 1px solid #cbd5e0;
      }

      .btn-secondary:hover {
        background: #cbd5e0;
        transform: translateY(-1px);
      }

      .examples {
        margin-top: 20px;
        padding: 15px;
        background: #f0f4f8;
        border-radius: 8px;
        border-left: 4px solid #667eea;
      }

      .examples h4 {
        color: #4a5568;
        margin-bottom: 10px;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .examples code {
        background: #2d3748;
        color: #e2e8f0;
        padding: 2px 6px;
        border-radius: 4px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        margin: 2px;
        display: inline-block;
      }

      .status {
        margin-top: 15px;
        padding: 10px;
        border-radius: 6px;
        font-size: 13px;
        text-align: center;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .status.show {
        opacity: 1;
      }

      .status.success {
        background: #c6f6d5;
        color: #2f855a;
        border: 1px solid #9ae6b4;
      }

      .status.error {
        background: #fed7d7;
        color: #c53030;
        border: 1px solid #feb2b2;
      }

      @media (max-width: 480px) {
        .container {
          padding: 15px;
          margin: 10px;
        }

        .button-group {
          flex-direction: column;
        }

        .btn {
          width: 100%;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h2>🧮 LaTeX Formula Plugin</h2>
        <p>Insert mathematical formulas using LaTeX syntax into content controls</p>
      </div>

      <div class="form-group">
        <label for="latexInput">LaTeX Formula:</label>
        <div class="textarea-container">
          <textarea
            id="latexInput"
            placeholder="Enter your LaTeX formula here, e.g., \frac{a}{b} + \sqrt{c^2 + d^2}"
            spellcheck="false"
          ></textarea>
        </div>
      </div>

      <div class="form-group">
        <label>Preview:</label>
        <div class="preview-section" id="preview">
          <div class="preview-placeholder">Enter LaTeX formula above to see preview</div>
        </div>
      </div>

      <div class="examples">
        <h4>Common Examples:</h4>
        <div>
          <code>\frac{a}{b}</code>
          <code>x^2 + y^2 = z^2</code>
          <code>\sqrt{x^2 + y^2}</code>
          <code>\sum_{i=1}^n i</code>
          <code>\int_0^1 x^2 dx</code>
        </div>
      </div>

      <div class="button-group">
        <button class="btn btn-primary" id="insertBtn">Insert Formula</button>
        <button class="btn btn-secondary" id="updatePreview">Update Preview</button>
      </div>

      <div class="status" id="status"></div>
    </div>

    <script>
      // Plugin initialization
      (function (window, undefined) {
        let latexInput, previewDiv, insertBtn, updatePreviewBtn, statusDiv;
        let currentContentControl = null;

        // Initialize plugin
        window.Asc.plugin.init = function () {
          console.log('LaTeX Formula Plugin initialized');

          latexInput = document.getElementById('latexInput');
          previewDiv = document.getElementById('preview');
          insertBtn = document.getElementById('insertBtn');
          updatePreviewBtn = document.getElementById('updatePreview');
          statusDiv = document.getElementById('status');

          // Event listeners
          latexInput.addEventListener('input', debounce(updatePreview, 500));
          latexInput.addEventListener('keydown', function (e) {
            if (e.ctrlKey && e.key === 'Enter') {
              insertFormula();
            }
          });

          insertBtn.addEventListener('click', insertFormula);
          updatePreviewBtn.addEventListener('click', updatePreview);

          // Load sample formula
          latexInput.value = '\\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}';
          updatePreview();

          // Configure MathJax
          configureMathJax();
        };

        // Configure MathJax for LaTeX rendering
        function configureMathJax() {
          if (window.MathJax) {
            window.MathJax.config = window.MathJax.config || {};
            window.MathJax.config.tex = {
              inlineMath: [
                ['$', '$'],
                ['\\(', '\\)'],
              ],
              displayMath: [
                ['$$', '$$'],
                ['\\[', '\\]'],
              ],
              processEscapes: true,
              processEnvironments: true,
            };
            window.MathJax.config.svg = {
              fontCache: 'global',
            };
          }
        }

        // Update preview with LaTeX rendering
        function updatePreview() {
          const latex = latexInput.value.trim();

          if (!latex) {
            previewDiv.innerHTML = '<div class="preview-placeholder">Enter LaTeX formula above to see preview</div>';
            return;
          }

          try {
            // Wrap in display math delimiters
            const mathExpression = `$$${latex}$$`;
            previewDiv.innerHTML = mathExpression;

            // Re-render MathJax
            if (window.MathJax && window.MathJax.typesetPromise) {
              window.MathJax.typesetPromise([previewDiv])
                .then(() => {
                  console.log('MathJax rendering complete');
                })
                .catch(err => {
                  console.error('MathJax rendering error:', err);
                  showStatus('Preview error: Invalid LaTeX syntax', 'error');
                });
            }
          } catch (error) {
            console.error('Preview error:', error);
            showStatus('Preview error: Invalid LaTeX syntax', 'error');
          }
        }

        // Insert formula into document using content control
        function insertFormula() {
          const latex = latexInput.value.trim();

          if (!latex) {
            showStatus('Please enter a LaTeX formula', 'error');
            return;
          }

          try {
            // Create content control with LaTeX formula
            const contentControlData = {
              Props: {
                Id: Math.floor(Math.random() * 1000000),
                Tag: 'latex_formula_' + Date.now(),
                Lock: 0, // 0 = unlocked, 1 = locked
                InternalId: null,
              },
              Script: `
                            // Insert LaTeX formula as equation
                            (function() {
                                try {
                                    var oDocument = Api.GetDocument();
                                    var oParagraph = Api.CreateParagraph();
                                    
                                    // Create equation using LaTeX
                                    var oMath = Api.CreateMath();
                                    oMath.ConvertLaTeX("${escapeLatex(latex)}");
                                    
                                    oParagraph.AddElement(oMath);
                                    
                                    // Insert into content control
                                    var oContentControl = Api.CreateContentControl(1); // 1 = RichText
                                    oContentControl.GetElement(0).Push(oParagraph);
                                    oContentControl.SetTag("latex_formula_${Date.now()}");
                                    oContentControl.SetTitle("LaTeX Formula: ${escapeForTitle(latex)}");
                                    
                                    oDocument.InsertContent([oContentControl]);
                                    
                                    return true;
                                } catch (error) {
                                    console.error('Formula insertion error:', error);
                                    return false;
                                }
                            })();
                        `,
            };

            // Execute script to insert content control
            window.Asc.plugin.executeMethod('ExecuteMethod', ['script', contentControlData.Script], function (result) {
              if (result && result.returnValue !== false) {
                showStatus('Formula inserted successfully!', 'success');

                // Optionally close plugin or clear input
                setTimeout(() => {
                  latexInput.value = '';
                  updatePreview();
                }, 1000);
              } else {
                showStatus('Error inserting formula. Please try again.', 'error');
              }
            });
          } catch (error) {
            console.error('Insert formula error:', error);
            showStatus('Error: ' + error.message, 'error');
          }
        }

        // Utility functions
        function escapeLatex(latex) {
          return latex.replace(/\\/g, '\\\\').replace(/"/g, '\\"');
        }

        function escapeForTitle(latex) {
          return latex.replace(/"/g, '\\"').substring(0, 50) + (latex.length > 50 ? '...' : '');
        }

        function debounce(func, wait) {
          let timeout;
          return function executedFunction(...args) {
            const later = () => {
              clearTimeout(timeout);
              func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
          };
        }

        function showStatus(message, type) {
          statusDiv.textContent = message;
          statusDiv.className = `status ${type} show`;

          setTimeout(() => {
            statusDiv.classList.remove('show');
          }, 3000);
        }

        // Plugin button handlers - removed as buttons are now defined in HTML
        window.Asc.plugin.button = function (id) {
          // No predefined buttons in config, handle if needed
          console.log('Button clicked:', id);
        };

        // Plugin resize handler
        window.Asc.plugin.onResize = function () {
          console.log('Plugin resized');
        };
      })(window, undefined);
    </script>
  </body>
</html>
